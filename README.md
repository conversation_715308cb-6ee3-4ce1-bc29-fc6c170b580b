# 🎨 Распознавание рукописных цифр с Streamlit

Интерактивное веб-приложение для распознавания рукописных цифр (0-9) с использованием машинного обучения и Streamlit.

## 📋 Описание

Это приложение позволяет пользователям рисовать цифры на интерактивном холсте, а обученная модель машинного обучения распознает нарисованную цифру в реальном времени. Проект включает в себя:

- **Интерактивный холст** для рисования цифр
- **Предобученную модель** для распознавания цифр (обучена на датасете MNIST)
- **Визуализацию результатов** с показом уверенности модели
- **Настройки рисования** (толщина линии, цвета)
- **Многоязычную поддержку** (русский и английский интерфейс)

## 🚀 Возможности

- ✏️ Рисование цифр на интерактивном холсте
- 🤖 Мгновенное распознавание нарисованных цифр
- 📊 Отображение вероятностей для всех цифр (0-9)
- 🎨 Настройка параметров рисования (толщина линии, цвета)
- 🔄 Автоматическая обработка и центрирование изображения
- 📈 Визуализация уверенности модели

## 🛠️ Технологии

- **Python 3.7+**
- **Streamlit** - для создания веб-интерфейса
- **OpenCV** - для обработки изображений
- **NumPy** - для работы с массивами
- **Scikit-learn** - для модели машинного обучения
- **Matplotlib** - для визуализации
- **streamlit-drawable-canvas** - для интерактивного холста

## 📦 Установка

1. **Клонируйте репозиторий:**
```bash
git clone https://github.com/PtOlga/StreamLit_test.git
cd StreamLit_test
```

2. **Создайте виртуальное окружение (рекомендуется):**
```bash
python -m venv venv
source venv/bin/activate  # На Windows: venv\Scripts\activate
```

3. **Установите зависимости:**
```bash
pip install -r requirements.txt
```

## 🎯 Запуск приложения

### Русская версия:
```bash
streamlit run predict_num.py
```

### Английская версия:
```bash
streamlit run predict-num-en.py
```

Приложение откроется в браузере по адресу `http://localhost:8501`

## 📁 Структура проекта

```
StreamLit_test/
├── predict_num.py              # Основное приложение (русский)
├── predict-num-en.py           # Английская версия приложения
├── Create-model.ipynb          # Jupyter notebook для создания модели
├── best_model.pkl              # Предобученная модель
├── requirements.txt            # Зависимости Python
├── test-load-big-model.py      # Тестирование загрузки больших моделей
├── images/                     # Папка с изображениями
│   ├── 1.png
│   ├── 3.png
│   ├── matrix.png
│   └── ...
├── models/                     # Папка с моделями
│   └── best_model.joblib
└── README.md                   # Этот файл
```

## 🔧 Как это работает

1. **Рисование:** Пользователь рисует цифру на холсте размером 280x280 пикселей
2. **Предобработка:** Изображение конвертируется в черно-белое, центрируется и масштабируется до 28x28 пикселей
3. **Нормализация:** Пиксели нормализуются в диапазон [0, 1]
4. **Предсказание:** Обученная модель анализирует изображение и выдает вероятности для каждой цифры
5. **Результат:** Отображается предсказанная цифра с уровнем уверенности и гистограммой вероятностей

## 🧠 О модели

Модель обучена на знаменитом датасете MNIST, который содержит 70,000 изображений рукописных цифр. Модель достигает высокой точности распознавания благодаря:

- Предобработке изображений (центрирование, нормализация)
- Использованию алгоритма Random Forest
- Оптимизации гиперпараметров

## 🎨 Использование

1. Откройте приложение в браузере
2. Настройте параметры рисования в боковой панели (по желанию)
3. Нарисуйте цифру от 0 до 9 на холсте
4. Посмотрите результат распознавания и уровень уверенности модели
5. Изучите гистограмму вероятностей для всех цифр
6. Нажмите "Очистить холст" для нового рисунка

## 🤝 Вклад в проект

Приветствуются любые улучшения! Вы можете:

- Сообщить об ошибках
- Предложить новые функции
- Улучшить модель машинного обучения
- Добавить поддержку других языков
- Улучшить интерфейс

## 📄 Лицензия

Этот проект распространяется под лицензией MIT. См. файл `LICENSE` для подробностей.

## 👨‍💻 Автор

**PtOlga** - [GitHub](https://github.com/PtOlga)

## 🙏 Благодарности

- Датасет MNIST
- Сообщество Streamlit
- Разработчики библиотек машинного обучения