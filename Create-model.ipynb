{"cells": [{"cell_type": "code", "execution_count": 1, "id": "d1712211-3cdf-4383-8fad-071272fddcec", "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 's<PERSON><PERSON><PERSON>'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 4\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m## Steg 1: Ladda in data\u001b[39;00m\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mnumpy\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mnp\u001b[39;00m\n\u001b[1;32m----> 4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01msklearn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdatasets\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m fetch_openml\n\u001b[0;32m      6\u001b[0m \u001b[38;5;66;03m# Ladda MNIST-datan\u001b[39;00m\n\u001b[0;32m      7\u001b[0m mnist \u001b[38;5;241m=\u001b[39m fetch_openml(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmnist_784\u001b[39m\u001b[38;5;124m'\u001b[39m, version\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m1\u001b[39m, cache\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m, as_frame\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m)\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named 'sklearn'"]}], "source": ["## Steg 1: La<PERSON><PERSON> in data\n", "\n", "import numpy as np\n", "from sklearn.datasets import fetch_openml\n", "\n", "# Ladda MNIST-datan\n", "mnist = fetch_openml('mnist_784', version=1, cache=True, as_frame=False)\n", "# print(mnist.descr)\n", "print(mnist.details)  # Använd rätt attribut :)\n", "\n", "X = mnist['data']\n", "y = mnist['target'].astype(np.uint8)\n", "\n", "# Mata ut de första 5 raderna med data som en DataFrame\n", "# import pandas as pd\n", "# df = pd.DataFrame(X)\n", "# print(df.head())"]}, {"cell_type": "code", "execution_count": 11, "id": "0fc84cdb-ea4e-4a46-97dd-c83d607a17ff", "metadata": {}, "outputs": [], "source": ["# Dela upp datan i tränings- och testset\n", "X_train, X_test, y_train, y_test = X[:60000], X[60000:], y[:60000], y[60000:]\n", "\n", "# dä<PERSON><PERSON>r att Jag har inte tillräckligt med ledigt minne på min bärbara dator, \n", "# då kommer jag att minska urvalet och samtidigt jämföra resultaten av modellerna på prover av olika datastorlekar\n", "# X_train, X_test, y_train, y_test = X[:10000], X[10000:], y[:10000], y[10000:]\n"]}, {"cell_type": "code", "execution_count": null, "id": "a16de9bc-bc28-4620-b260-d481c05c49cd", "metadata": {}, "outputs": [], "source": ["## Steg 2: <PERSON><PERSON><PERSON><PERSON> och utvärdera modeller\n", "\n", "# Modell 1: Random Forest Classifier\n", "\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import accuracy_score, classification_report\n", "\n", "# <PERSON><PERSON><PERSON> och träna modellen\n", "rf_clf = RandomForestClassifier(n_estimators=100, random_state=42)\n", "rf_clf.fit(X_train, y_train)\n", "\n", "# <PERSON><PERSON>r prediktioner på testdata\n", "y_pred_rf = rf_clf.predict(X_test)\n", "\n", "#print(\"Classification Report:\\n\", classification_report(y_test, y_pred_rf))"]}, {"cell_type": "code", "execution_count": null, "id": "11f691bc-d2ad-4cf5-9474-4e8aedb08241", "metadata": {}, "outputs": [], "source": ["## Steg 3: Jämföra modeller och välja den bästa\n", "print(\"Random Forest Accuracy:\", accuracy_score(y_test, y_pred_rf))\n", "#print(\"SVM Accuracy:\", accuracy_score(y_test, y_pred_svm))"]}, {"cell_type": "code", "execution_count": 23, "id": "f5a475e2-b96f-4fc8-b12d-f55404a4c514", "metadata": {}, "outputs": [], "source": ["### Steg 4: <PERSON><PERSON> den bästa modellen med `joblib` \n", "\n", "import joblib\n", "\n", "# Spara den bästa modellen\n", "joblib.dump(rf_clf, 'best_model.joblib')\n", "\n", "# Ladda modellen senare\n", "loaded_model = joblib.load('best_model.joblib')\n", "\n", "# Använd den laddade modellen för prediktioner\n", "predictions = loaded_model.predict(X_test)\n"]}], "metadata": {"kernelspec": {"display_name": "base312", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 5}